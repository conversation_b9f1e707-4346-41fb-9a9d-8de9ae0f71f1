<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title data-i18n="titles.promptEditor">提示词编辑器 - 文档分析系统</title>
    <link rel="stylesheet" href="/static/style.css">
    <script src="/static/js/i18n.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* 提示词编辑器特有样式 */

        /* 优化整体布局和间距 */
        .prompt-editor {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        /* 优化头部样式 */
        .prompt-header {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            text-align: center;
            border: 1px solid var(--border-color);
        }

        .prompt-header h1 {
            color: #333;
            margin: 0 0 10px 0;
            font-size: 2.5rem;
            font-weight: 700;
            line-height: 1.2;
        }

        .prompt-header p {
            color: var(--text-muted);
            margin: 0;
            font-size: 1.1rem;
        }


        
        /* 优化控制面板 */
        .prompt-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
            align-items: end;
        }
        
        .control-group {
            margin-bottom: 0;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        select, input, textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            font-size: 14px;
            background: white;
            transition: var(--transition);
        }

        [data-theme="dark"] select,
        [data-theme="dark"] input,
        [data-theme="dark"] textarea {
            background: var(--light-color);
            color: var(--text-color);
            border-color: var(--border-color);
        }

        select:focus, input:focus, textarea:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        /* 优化提示词列表 */
        .prompt-list {
            background: white;
            border-radius: 12px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: var(--shadow-md);
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
        }

        [data-theme="dark"] .prompt-list {
            background: var(--light-color);
        }
        
        .prompt-item {
            padding: 20px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            margin-bottom: 15px;
            cursor: pointer;
            transition: var(--transition);
            background: white;
            position: relative;
            overflow: hidden;
        }

        [data-theme="dark"] .prompt-item {
            background: var(--light-color);
        }
        
        .prompt-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--primary-color);
            transform: scaleY(0);
            transition: var(--transition);
        }
        
        .prompt-item:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
            border-color: var(--primary-color);
        }

        .prompt-item:hover::before {
            transform: scaleY(1);
        }
        
        .prompt-item.selected {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-color: var(--primary-color);
            box-shadow: var(--shadow-md);
        }

        .prompt-item.selected::before {
            transform: scaleY(1);
            background: rgba(255, 255, 255, 0.3);
        }
        
        .prompt-item.inactive {
            opacity: 0.7;
            background: var(--light-color);
        }
        
        .prompt-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .prompt-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }
        
        .tag {
            background: var(--light-color);
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            color: var(--text-color);
            border: 1px solid var(--border-color);
        }
        
        .tag.primary {
            background: var(--primary-color);
            color: white;
            border-color: var(--primary-color);
        }
        
        /* 优化编辑器区域 */
        .editor-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .editor-panel {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: var(--shadow-md);
            border: 1px solid var(--border-color);
            transition: var(--transition);
        }

        .editor-panel:hover {
            box-shadow: var(--shadow-lg);
        }

        [data-theme="dark"] .editor-panel {
            background: var(--light-color);
        }
        
        .editor-panel h3 {
            margin: 0 0 25px 0;
            color: var(--text-color);
            border-bottom: 2px solid var(--primary-color);
            padding-bottom: 15px;
            font-size: 1.3rem;
            font-weight: 600;
        }
        
        .form-group {
            margin-bottom: 25px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--text-color);
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 15px;
            border: 2px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            font-size: 14px;
            transition: var(--transition);
            background: white;
        }

        [data-theme="dark"] .form-group input,
        [data-theme="dark"] .form-group textarea,
        [data-theme="dark"] .form-group select {
            background: var(--light-color);
            color: var(--text-color);
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .form-group textarea {
            resize: vertical;
            min-height: 200px;
            font-family: 'Courier New', monospace;
            line-height: 1.5;
        }
        
        /* 优化按钮样式 */
        .btn {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border: none;
            padding: 14px 28px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 15px;
            font-weight: 500;
            transition: var(--transition);
            margin-right: 12px;
            margin-bottom: 12px;
            box-shadow: var(--shadow-sm);
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn:active {
            transform: translateY(0);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d, #5a6268);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color), #c82333);
        }
        
        .btn-success {
            background: linear-gradient(135deg, var(--success-color), #218838);
        }
        
        /* 优化比较和测试区域 */
        .comparison-section,
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: var(--shadow-md);
            margin-bottom: 30px;
            border: 1px solid var(--border-color);
        }

        .comparison-section h3,
        .test-section h3 {
            margin-top: 0;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--primary-color);
        }

        .comparison-section:hover,
        .test-section:hover {
            box-shadow: var(--shadow-lg);
        }

        [data-theme="dark"] .comparison-section,
        [data-theme="dark"] .test-section {
            background: var(--light-color);
        }
        
        .comparison-controls,
        .test-controls {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .comparison-result,
        .test-result {
            background: var(--light-color);
            border-radius: var(--border-radius-sm);
            padding: 25px;
            margin-top: 20px;
            border: 1px solid var(--border-color);
        }
        
        .similarity-score {
            font-size: 2rem;
            font-weight: bold;
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            border-radius: var(--border-radius-sm);
            background: white;
            box-shadow: var(--shadow-sm);
        }

        [data-theme="dark"] .similarity-score {
            background: var(--light-color);
        }
        
        .score-high { color: var(--success-color); }
        .score-medium { color: var(--warning-color); }
        .score-low { color: var(--danger-color); }
        
        .result-metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .metric {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: var(--border-radius-sm);
            border: 1px solid var(--border-color);
            box-shadow: var(--shadow-sm);
            transition: var(--transition);
        }

        .metric:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        [data-theme="dark"] .metric {
            background: var(--light-color);
        }
        
        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 5px;
        }
        
        .metric-label {
            font-size: 0.9rem;
            color: var(--text-muted);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: var(--border-radius);
            width: 80%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: var(--shadow-lg);
            border: 1px solid var(--border-color);
        }

        [data-theme="dark"] .modal-content {
            background: var(--light-color);
            color: var(--text-color);
        }
        
        .close {
            color: var(--text-muted);
            float: right;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: var(--transition);
        }
        
        .close:hover {
            color: var(--text-color);
            transform: scale(1.1);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .spinner {
            border: 4px solid var(--border-color);
            border-top: 4px solid var(--primary-color);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .diff-view {
            background: var(--light-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            line-height: 1.6;
        }
        
        .diff-added { 
            background: rgba(40, 167, 69, 0.1); 
            border-left: 3px solid var(--success-color);
            padding: 2px 4px;
        }
        
        .diff-removed {
            background: rgba(220, 53, 69, 0.1);
            border-left: 3px solid var(--danger-color);
            padding: 2px 4px;
            text-decoration: line-through;
        }

        /* Prompt Content Display Styles */
        .prompt-content-display {
            margin: 25px 0;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
        }

        .prompt-content-item h5 {
            margin: 0 0 10px 0;
            color: var(--text-color);
            font-weight: 600;
        }

        .prompt-content-box {
            background: var(--light-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 20px;
            min-height: 250px;
            max-height: 400px;
            overflow-y: auto;
        }

        .prompt-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid var(--border-color);
        }

        .prompt-name {
            font-weight: 600;
            color: var(--primary-color);
        }

        .prompt-type {
            background: var(--accent-color);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
        }

        .prompt-template {
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.6;
            white-space: pre-wrap;
            color: var(--text-color);
        }

        /* Document Selection Styles */
        .document-selection-wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }

        .document-selection-wrapper select {
            flex: 1;
            min-width: 200px;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius-sm);
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .btn-upload {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: var(--border-radius-sm);
            font-size: 13px;
            cursor: pointer;
            transition: var(--transition);
            white-space: nowrap;
            flex: 0 0 auto;
        }

        .btn-upload:hover {
            background: linear-gradient(135deg, #218838, #1ea085);
            transform: translateY(-1px);
        }

        .or-divider {
            color: var(--text-muted);
            font-size: 14px;
            font-weight: 500;
        }

        /* Version Comparison Styles */
        .comparison-header {
            background: var(--light-color);
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            padding: 15px;
            margin-bottom: 20px;
        }

        .prompt-comparison-info {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .prompt-info-item {
            font-size: 14px;
            color: var(--text-color);
        }

        .prompt-info-item strong {
            color: var(--primary-color);
        }

        .version-comparison-notice {
            background: var(--accent-color);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 13px;
            text-align: center;
            margin-top: 10px;
        }

        @media (max-width: 768px) {
            .prompt-content-display {
                grid-template-columns: 1fr;
            }

            .document-selection-wrapper {
                flex-direction: column;
                align-items: stretch;
            }

            .or-divider {
                text-align: center;
                margin: 5px 0;
            }
        }

        @media (min-width: 768px) {
            .prompt-comparison-info {
                flex-direction: row;
                justify-content: space-between;
                align-items: center;
            }

            .version-comparison-notice {
                margin-top: 0;
                align-self: center;
            }
        }



        /* 响应式设计 */
        @media (max-width: 1200px) {
            .editor-section {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 768px) {
            .prompt-controls {
                grid-template-columns: 1fr;
            }
            
            .control-group {
                min-width: unset;
            }

            .comparison-controls,
            .test-controls {
                grid-template-columns: 1fr;
            }

            .prompt-header {
                padding: 30px 20px;
            }

            .prompt-header h1 {
                font-size: 2rem;
            }

            .main-nav {
                flex-direction: column;
                align-items: center;
            }
        }

        @media (max-width: 480px) {
            .prompt-editor {
                padding: 10px;
            }
            
            .editor-panel,
            .prompt-list,
            .comparison-section,
            .test-section {
                padding: 20px;
            }

            .comparison-section .btn,
            .test-section .btn {
                width: 100%;
                margin-bottom: 10px;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-color);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--primary-dark);
        }

        /* 打印样式 */
        @media print {
            .btn {
                display: none;
            }
            
            .editor-panel,
            .prompt-list,
            .comparison-section,
            .test-section {
                box-shadow: none;
                border: 1px solid #ccc;
            }
        }

        /* 优化按钮组样式 */
        .control-group {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            align-items: center;
        }

        .control-group .btn {
            margin: 0;
            flex: 1 1 auto;
            min-width: 180px;
            white-space: nowrap;
        }

        .button-group {
            display: flex;
            gap: 12px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .button-group .btn {
            margin: 0;
            flex: 0 0 auto;
            white-space: nowrap;
        }

        .filter-row {
            display: flex;
            gap: 20px;
        }

        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
            
            .button-group .btn {
                width: 100%;
            }
            
            .filter-row {
                flex-direction: column;
                gap: 15px;
            }
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .control-group {
                flex-direction: column;
                align-items: stretch;
            }
            
            .control-group .btn {
                width: 100%;
                min-width: unset;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <h1 data-i18n="titles.systemName">📄 智能文档分析系统</h1>
            <p class="subtitle" data-i18n="titles.subtitle">基于智谱AI的政策文档深度分析工具</p>
            <nav class="main-nav">
                <a href="/" class="nav-link" data-i18n="nav.home">🏠 Document Analysis</a>
                <a href="/static/visualization-en.html" class="nav-link" data-i18n="nav.visualization">📊 Data Visualization</a>
                <a href="/static/data-table-en.html" class="nav-link" data-i18n="nav.tableView">📋 Table View</a>
                <a href="/static/document-management-en.html" class="nav-link" data-i18n="nav.documentManagement">📁 Document Management</a>
                <a href="/static/prompts-en.html" class="nav-link active" data-i18n="nav.promptEditor">🔧 Prompt Editor</a>
                <a href="/docs" class="nav-link" data-i18n="nav.apiDocs">📚 API Documentation</a>
                <button class="language-toggle" onclick="toggleLanguage()" title="Switch Language">中</button>
                <button class="theme-toggle" onclick="toggleTheme()" data-i18n="analysis.switchTheme" title="Switch Theme">
                    <i>🌙</i>
                </button>
            </nav>
        </header>

        <main>
            <div class="prompt-editor">
                <div class="prompt-header">
                    <h1 data-i18n="nav.promptEditor">🔧 提示词编辑器</h1>
                    <p data-i18n="prompts.description">创建、编辑和优化您的AI提示词模板</p>
                </div>

                <!-- 控制面板 -->
<div class="prompt-controls">
    <div class="button-group">
        <button class="btn" onclick="createNewPrompt()" data-i18n="prompts.createNew">Create New Prompt</button>
        <button class="btn btn-secondary" onclick="refreshPromptList()">🔄 Refresh List</button>
        <button class="btn btn-success" onclick="initializeDefaultPrompts()" data-i18n="prompts.loadDefaults">Load Default Templates</button>
    </div>
    
    <div class="filter-row">
        <div class="control-group">
            <label for="task-filter">Filter by Task Type:</label>
            <select id="task-filter">
                <option value="">All Types</option>
                <option value="actor_relation">Actor Relations</option>
                <option value="role_framing">Role Framing</option>
                <option value="problem_scope">Problem Scope</option>
                <option value="causal_mechanism">Causal Mechanism</option>
            </select>
        </div>
        
        <div class="control-group">
            <label for="status-filter">Filter by Status:</label>
            <select id="status-filter">
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
            </select>
        </div>
    </div>
</div>

        <!-- 提示词列表 -->
        <div class="prompt-list" id="prompt-list">
            <div class="loading">
                <div class="spinner"></div>
                <p>加载提示词列表...</p>
            </div>
        </div>

        <!-- 编辑器区域 -->
        <div class="editor-section" id="editor-section" style="display: none;">
            <div class="editor-panel">
                <h3>✏️ 编辑提示词</h3>
                <form id="prompt-form">
                    <div class="form-group">
                        <label for="prompt-name">提示词名称:</label>
                        <input type="text" id="prompt-name" required>
                    </div>
                    <div class="form-group">
                        <label for="prompt-task">任务类型:</label>
                        <select id="prompt-task" required>
                            <option value="">请选择</option>
                            <option value="actor_relation">行为者关系</option>
                            <option value="role_framing">角色塑造</option>
                            <option value="problem_scope">问题范围</option>
                            <option value="causal_mechanism">因果机制</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="prompt-description">描述:</label>
                        <input type="text" id="prompt-description" required>
                    </div>
                    <div class="form-group">
                        <label for="prompt-template">提示词模板:</label>
                        <textarea id="prompt-template" required placeholder="请输入提示词模板，使用 {document} 作为文档内容占位符"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="prompt-tags">标签 (用逗号分隔):</label>
                        <input type="text" id="prompt-tags" placeholder="例如: 优化, 详细, 简洁">
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="prompt-active"> 激活使用
                        </label>
                    </div>
                    <div>
                        <button type="submit" class="btn btn-success">💾 保存</button>
                        <button type="button" class="btn btn-secondary" onclick="cancelEdit()">❌ 取消</button>
                        <button type="button" class="btn btn-danger" onclick="deletePrompt()" id="delete-btn" style="display: none;">🗑️ 删除</button>
                    </div>
                </form>
            </div>
            
            <div class="editor-panel">
                <h3>📋 提示词预览</h3>
                <div class="form-group">
                    <label>预览文档内容:</label>
                    <textarea id="preview-document" rows="4" placeholder="输入测试文档内容以预览效果">政府将加强对科技企业的监管，确保其合规经营。同时，政府也将为科技创新提供更多的支持和资源。</textarea>
                </div>
                <div class="form-group">
                    <label>格式化后预览:</label>
                    <div id="formatted-preview" style="background: #f8f9fa; border: 1px solid #ddd; border-radius: 8px; padding: 15px; min-height: 200px; white-space: pre-wrap; font-family: 'Courier New', monospace; font-size: 12px;"></div>
                </div>
                <button class="btn" onclick="updatePreview()">🔄 更新预览</button>
            </div>
        </div>

        <!-- Prompt Comparison -->
        <div class="comparison-section">
            <h3 data-i18n="prompts.comparison">🔍 提示词比较</h3>
            <div class="comparison-controls">
                <div class="form-group">
                    <label for="compare-prompt1" data-i18n="prompts.prompt1">提示词 1:</label>
                    <select id="compare-prompt1" onchange="showPromptContent(1, this.value)">
                        <option value="" data-i18n="prompts.selectPrompt">选择提示词</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="compare-prompt2" data-i18n="prompts.prompt2">提示词 2:</label>
                    <select id="compare-prompt2" onchange="showPromptContent(2, this.value)">
                        <option value="" data-i18n="prompts.selectPrompt">选择提示词</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="compare-document" data-i18n="prompts.testDocument">测试文档:</label>
                    <div class="document-selection-wrapper">
                        <select id="compare-doc-select" onchange="loadDocumentForComparison(this.value)">
                            <option value="" data-i18n="prompts.selectDocument">-- 选择已有文档 --</option>
                        </select>
                        <button type="button" class="btn btn-upload" onclick="triggerFileUpload('compare')">
                            📁 Upload File
                        </button>
                        <input type="file" id="compare-file-input" accept=".txt,.doc,.docx,.pdf" style="display: none;" onchange="handleFileUpload(this, 'compare')">
                        <span class="or-divider" data-i18n="prompts.or">或</span>
                    </div>
                    <textarea id="compare-document" rows="3" data-i18n="prompts.testDocumentPlaceholder" placeholder="输入用于比较的测试文档">政府将加强对科技企业的监管，确保其合规经营。同时，政府也将为科技创新提供更多的支持和资源。</textarea>
                </div>
            </div>

            <!-- 提示词内容显示框 -->
            <div class="prompt-content-display">
                <div class="prompt-content-item">
                    <h5 data-i18n="prompts.prompt1Content">提示词 1 内容:</h5>
                    <div id="prompt1-content" class="prompt-content-box">
                        <div class="prompt-info">
                            <span id="prompt1-name" class="prompt-name" data-i18n="prompts.selectPromptFirst">请选择提示词</span>
                            <span id="prompt1-type" class="prompt-type"></span>
                        </div>
                        <div id="prompt1-template" class="prompt-template" data-i18n="prompts.contentWillShow">选择提示词后将在此显示完整内容...</div>
                    </div>
                </div>
                <div class="prompt-content-item">
                    <h5 data-i18n="prompts.prompt2Content">提示词 2 内容:</h5>
                    <div id="prompt2-content" class="prompt-content-box">
                        <div class="prompt-info">
                            <span id="prompt2-name" class="prompt-name" data-i18n="prompts.selectPromptFirst">请选择提示词</span>
                            <span id="prompt2-type" class="prompt-type"></span>
                        </div>
                        <div id="prompt2-template" class="prompt-template" data-i18n="prompts.contentWillShow">选择提示词后将在此显示完整内容...</div>
                    </div>
                </div>
            </div>
            <button class="btn" onclick="comparePrompts()" data-i18n="prompts.startComparison">🔍 开始比较</button>

            <div id="comparison-result" class="comparison-result" style="display: none;">
                <h4 data-i18n="prompts.comparisonResult">比较结果</h4>
                <div id="comparison-content"></div>
            </div>
        </div>

        <!-- 提示词测试 -->
        <div class="test-section">
            <h3 data-i18n="prompts.testing">🧪 提示词测试</h3>
            <div class="test-controls">
                <div class="form-group">
                    <label for="test-prompt" data-i18n="prompts.selectPrompt">选择提示词:</label>
                    <select id="test-prompt">
                        <option value="" data-i18n="prompts.selectPrompt">选择提示词</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="test-document" data-i18n="prompts.testDocument">测试文档:</label>
                    <div class="document-selection-wrapper">
                        <select id="test-doc-select" onchange="loadDocumentForTest(this.value)">
                            <option value="" data-i18n="prompts.selectDocument">-- 选择已有文档 --</option>
                        </select>
                        <button type="button" class="btn btn-upload" onclick="triggerFileUpload('test')">
                            📁 Upload File
                        </button>
                        <input type="file" id="test-file-input" accept=".txt,.doc,.docx,.pdf" style="display: none;" onchange="handleFileUpload(this, 'test')">
                        <span class="or-divider" data-i18n="prompts.or">或</span>
                    </div>
                    <textarea id="test-document" rows="3" data-i18n="prompts.testDocumentPlaceholder" placeholder="输入用于测试的文档">政府将加强对科技企业的监管，确保其合规经营。同时，政府也将为科技创新提供更多的支持和资源。</textarea>
                </div>
            </div>
            <button class="btn" onclick="testPrompt()" data-i18n="prompts.startTest">🧪 开始测试</button>

            <div id="test-result" class="test-result" style="display: none;">
                <h4 data-i18n="prompts.testResult">测试结果</h4>
                <div id="test-content"></div>
            </div>
        </div>
            </div>
        </main>
    </div>

    <!-- 加载提示词列表 -->
    <script>
        let prompts = [];
        let currentPromptId = null;

        // Page initialization
        document.addEventListener('DOMContentLoaded', function() {
            // 默认设置为英文
            switchLanguage('en');
            initTheme();
            
            // 加载提示词列表
            loadPromptList();
            
            // 设置事件监听器
            setupEventListeners();
        });

        function setupEventListeners() {
            // 移除之前可能存在的监听器
            document.getElementById('compare-prompt1').removeEventListener('change', showPrompt1Content);
            document.getElementById('compare-prompt2').removeEventListener('change', showPrompt2Content);
            
            // 设置新的监听器
            document.getElementById('compare-prompt1').addEventListener('change', showPrompt1Content);
            document.getElementById('compare-prompt2').addEventListener('change', showPrompt2Content);
            
            // 设置表单提交事件
            document.getElementById('prompt-form').addEventListener('submit', savePrompt);
            
            // 设置筛选器事件
            document.getElementById('task-filter').addEventListener('change', filterPrompts);
            document.getElementById('status-filter').addEventListener('change', filterPrompts);
            
            // 设置预览更新事件
            document.getElementById('prompt-template').addEventListener('input', updatePreview);
            document.getElementById('preview-document').addEventListener('input', updatePreview);
        }

        async function loadPromptList() {
            try {
                const response = await fetch('/api/v1/prompts/?active_only=false');
                if (response.ok) {
                    prompts = await response.json();

                    // 如果没有提示词，初始化默认提示词
                    if (prompts.length === 0) {
                        await initializeDefaultPrompts();
                        return; // initializeDefaultPrompts 会重新调用 loadPromptList
                    }

                    renderPromptList();
                    updatePromptSelects();
                } else {
                    alert('加载提示词列表失败');
                }
            } catch (error) {
                console.error('加载提示词列表失败:', error);
                alert('加载提示词列表失败');
            }
        }

        async function initializeDefaultPrompts() {
            try {
                const response = await fetch('/api/v1/prompts/initialize-defaults', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('默认提示词初始化成功:', result);

                    // 显示成功消息
                    alert(t('prompts.defaultsLoaded'));

                    // 重新加载提示词列表
                    await loadPromptList();
                } else {
                    console.error('初始化默认提示词失败');
                    alert('Failed to initialize default prompts');
                }
            } catch (error) {
                console.error('初始化默认提示词失败:', error);
            }
        }

        function renderPromptList() {
            const container = document.getElementById('prompt-list');
            const taskFilter = document.getElementById('task-filter').value;
            const statusFilter = document.getElementById('status-filter').value;
            
            let filteredPrompts = prompts;
            
            if (taskFilter) {
                filteredPrompts = filteredPrompts.filter(p => p.task_type === taskFilter);
            }
            
            if (statusFilter === 'active') {
                filteredPrompts = filteredPrompts.filter(p => p.is_active);
            } else if (statusFilter === 'inactive') {
                filteredPrompts = filteredPrompts.filter(p => !p.is_active);
            }
            
            if (filteredPrompts.length === 0) {
                container.innerHTML = '<p style="text-align: center; color: #666;">没有找到符合条件的提示词</p>';
                return;
            }
            
            container.innerHTML = filteredPrompts.map(prompt => `
                <div class="prompt-item ${prompt.id === currentPromptId ? 'selected' : ''} ${!prompt.is_active ? 'inactive' : ''}" 
                     onclick="selectPrompt('${prompt.id}')">
                    <div class="prompt-meta">
                        <strong>${prompt.name}</strong>
                        <span>v${prompt.version}</span>
                    </div>
                    <div class="prompt-meta">
                        <span>${getTaskTypeName(prompt.task_type)}</span>
                        <span>${prompt.is_active ? '✅ 激活' : '❌ 未激活'}</span>
                    </div>
                    <p style="margin: 10px 0; color: #666; font-size: 14px;">${prompt.description}</p>
                    <div class="prompt-tags">
                        ${prompt.tags.map(tag => `<span class="tag">${tag}</span>`).join('')}
                    </div>
                </div>
            `).join('');
        }

        function getTaskTypeName(taskType) {
            const names = {
                'actor_relation': '行为者关系',
                'role_framing': '角色塑造',
                'problem_scope': '问题范围',
                'causal_mechanism': '因果机制'
            };
            return names[taskType] || taskType;
        }

        function updatePromptSelects() {
            const selects = ['compare-prompt1', 'compare-prompt2', 'test-prompt'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                const currentValue = select.value;

                // Group prompts by name and task type
                const groupedPrompts = {};
                prompts.forEach(prompt => {
                    const key = `${prompt.name}_${prompt.task_type}`;
                    if (!groupedPrompts[key]) {
                        groupedPrompts[key] = [];
                    }
                    groupedPrompts[key].push(prompt);
                });

                // Sort prompts within each group by version
                Object.keys(groupedPrompts).forEach(key => {
                    groupedPrompts[key].sort((a, b) => {
                        // Sort by version number, newer versions first
                        return b.version.localeCompare(a.version, undefined, { numeric: true });
                    });
                });

                let optionsHtml = '<option value="">Select Prompt</option>';

                // Generate options with version information
                Object.keys(groupedPrompts).sort().forEach(key => {
                    const promptGroup = groupedPrompts[key];
                    promptGroup.forEach((prompt, index) => {
                        const versionLabel = promptGroup.length > 1 ? ` v${prompt.version}` : '';
                        const isDefault = prompt.is_default ? ' [Default]' : '';
                        const isActive = prompt.is_active ? '' : ' [Inactive]';

                        optionsHtml += `<option value="${prompt.id}">${prompt.name}${versionLabel} (${getTaskTypeName(prompt.task_type)})${isDefault}${isActive}</option>`;
                    });
                });

                select.innerHTML = optionsHtml;
                select.value = currentValue;
            });
        }

        function selectPrompt(promptId) {
            currentPromptId = promptId;
            const prompt = prompts.find(p => p.id === promptId);
            
            if (prompt) {
                // 填充表单
                document.getElementById('prompt-name').value = prompt.name;
                document.getElementById('prompt-task').value = prompt.task_type;
                document.getElementById('prompt-description').value = prompt.description;
                document.getElementById('prompt-template').value = prompt.template;
                document.getElementById('prompt-tags').value = prompt.tags.join(', ');
                document.getElementById('prompt-active').checked = prompt.is_active;
                
                // 显示编辑器
                document.getElementById('editor-section').style.display = 'grid';
                document.getElementById('delete-btn').style.display = 'inline-block';
                
                // 更新列表选中状态
                renderPromptList();
                
                // 更新预览
                updatePreview();
            }
        }

        function createNewPrompt() {
            currentPromptId = null;
            
            // 清空表单
            document.getElementById('prompt-form').reset();
            document.getElementById('editor-section').style.display = 'grid';
            document.getElementById('delete-btn').style.display = 'none';
            
            // 更新列表选中状态
            renderPromptList();
            
            // 更新预览
            updatePreview();
        }

        function cancelEdit() {
            currentPromptId = null;
            document.getElementById('editor-section').style.display = 'none';
            document.getElementById('prompt-form').reset();
            renderPromptList();
        }

        async function savePrompt(event) {
            event.preventDefault();
            
            const formData = {
                name: document.getElementById('prompt-name').value,
                task_type: document.getElementById('prompt-task').value,
                description: document.getElementById('prompt-description').value,
                template: document.getElementById('prompt-template').value,
                tags: document.getElementById('prompt-tags').value.split(',').map(tag => tag.trim()).filter(tag => tag),
                is_active: document.getElementById('prompt-active').checked
            };
            
            try {
                let response;
                if (currentPromptId) {
                    // 更新现有提示词
                    response = await fetch(`/api/v1/prompts/${currentPromptId}`, {
                        method: 'PUT',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });
                } else {
                    // 创建新提示词
                    response = await fetch('/api/v1/prompts/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });
                }
                
                if (response.ok) {
                    alert('保存成功');
                    await loadPromptList();
                    cancelEdit();
                } else {
                    const error = await response.json();
                    alert('保存失败: ' + (error.detail || '未知错误'));
                }
            } catch (error) {
                console.error('保存失败:', error);
                alert('保存失败');
            }
        }

        async function deletePrompt() {
            if (!currentPromptId) return;
            
            if (confirm('确定要删除这个提示词吗？')) {
                try {
                    const response = await fetch(`/api/v1/prompts/${currentPromptId}`, {
                        method: 'DELETE'
                    });
                    
                    if (response.ok) {
                        alert('删除成功');
                        await loadPromptList();
                        cancelEdit();
                    } else {
                        alert('删除失败');
                    }
                } catch (error) {
                    console.error('删除失败:', error);
                    alert('删除失败');
                }
            }
        }

        function updatePreview() {
            const template = document.getElementById('prompt-template').value;
            const documentText = document.getElementById('preview-document').value;
            
            const formatted = template.replace('{document}', documentText);
            document.getElementById('formatted-preview').textContent = formatted;
        }

        function filterPrompts() {
            renderPromptList();
        }

        async function comparePrompts() {
            console.log('Starting prompt comparison...');

            const prompt1Id = document.getElementById('compare-prompt1').value;
            const prompt2Id = document.getElementById('compare-prompt2').value;
            const documentText = document.getElementById('compare-document').value;

            console.log('Selected prompts:', { prompt1Id, prompt2Id, documentText: documentText.substring(0, 50) + '...' });

            if (!prompt1Id || !prompt2Id) {
                alert('Please select two prompts for comparison');
                return;
            }

            if (prompt1Id === prompt2Id) {
                alert('Please select two different prompts');
                return;
            }

            const resultDiv = document.getElementById('comparison-result');
            const contentDiv = document.getElementById('comparison-content');

            if (!resultDiv || !contentDiv) {
                console.error('Cannot find result display elements');
                alert('Page element error, please refresh and try again');
                return;
            }

            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>Comparing prompts...</p></div>';

            try {
                console.log('Sending API request...');
                const requestBody = {
                    prompt1_id: prompt1Id,
                    prompt2_id: prompt2Id,
                    test_document: documentText
                };
                console.log('Request body:', requestBody);

                const response = await fetch('/api/v1/prompts/compare', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(requestBody)
                });

                console.log('API response status:', response.status);

                if (response.ok) {
                    const result = await response.json();
                    console.log('Comparison result:', result);
                    renderComparisonResult(result);
                } else {
                    const errorText = await response.text();
                    console.error('API error response:', errorText);
                    try {
                        const error = JSON.parse(errorText);
                        contentDiv.innerHTML = `<p style="color: red;">Comparison failed: ${error.detail || 'Unknown error'}</p>`;
                    } catch (e) {
                        contentDiv.innerHTML = `<p style="color: red;">Comparison failed: HTTP ${response.status}</p>`;
                    }
                }
            } catch (error) {
                console.error('Comparison failed:', error);
                contentDiv.innerHTML = `<p style="color: red;">Comparison failed: ${error.message || 'Network error'}</p>`;
            }
        }

        function renderComparisonResult(result) {
            const contentDiv = document.getElementById('comparison-content');
            
            const similarityClass = result.similarity_score > 0.7 ? 'score-high' : 
                                  result.similarity_score > 0.4 ? 'score-medium' : 'score-low';
            
            contentDiv.innerHTML = `
                <div class="similarity-score ${similarityClass}">
                    相似度评分: ${(result.similarity_score * 100).toFixed(1)}%
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h5>推荐建议:</h5>
                    <p>${result.recommendation}</p>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h5>测试结果:</h5>
                    ${renderTestResults(result.differences.test_results)}
                </div>
                
                <div style="margin-bottom: 20px;">
                    <h5>文本差异:</h5>
                    <div class="diff-view">${result.differences.text_differences.diff_preview}</div>
                </div>
            `;
        }

        function renderTestResults(testResults) {
            let html = '<div class="result-metrics">';
            
            for (const [key, value] of Object.entries(testResults)) {
                const promptName = key === 'prompt1' ? '提示词1' : '提示词2';
                const success = value.success;
                const responseTime = value.response_time;
                const quality = value.result_quality;
                
                html += `
                    <div class="metric">
                        <div class="metric-value" style="color: ${success ? '#28a745' : '#dc3545'};">
                            ${success ? '✅' : '❌'}
                        </div>
                        <div class="metric-label">${promptName}</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${responseTime.toFixed(2)}s</div>
                        <div class="metric-label">响应时间</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${(quality * 100).toFixed(0)}%</div>
                        <div class="metric-label">质量评分</div>
                    </div>
                `;
            }
            
            html += '</div>';
            return html;
        }

        async function testPrompt() {
            const promptId = document.getElementById('test-prompt').value;
            const documentText = document.getElementById('test-document').value;
            
            if (!promptId) {
                alert('请选择要测试的提示词');
                return;
            }
            
            const resultDiv = document.getElementById('test-result');
            const contentDiv = document.getElementById('test-content');
            
            resultDiv.style.display = 'block';
            contentDiv.innerHTML = '<div class="loading"><div class="spinner"></div><p>正在测试提示词...</p></div>';
            
            try {
                const response = await fetch(`/api/v1/prompts/test/${promptId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        test_document: documentText,
                        document_id: 'test_doc_' + Date.now()
                    })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    renderTestResult(result);
                } else {
                    const error = await response.json();
                    contentDiv.innerHTML = `<p style="color: red;">测试失败: ${error.detail || '未知错误'}</p>`;
                }
            } catch (error) {
                console.error('测试失败:', error);
                contentDiv.innerHTML = '<p style="color: red;">测试失败</p>';
            }
        }

        function renderTestResult(result) {
            const contentDiv = document.getElementById('test-content');
            
            const qualityClass = result.result_quality > 0.7 ? 'score-high' : 
                               result.result_quality > 0.4 ? 'score-medium' : 'score-low';
            
            contentDiv.innerHTML = `
                <div class="result-metrics">
                    <div class="metric">
                        <div class="metric-value" style="color: ${result.json_validity ? '#28a745' : '#dc3545'};">
                            ${result.json_validity ? '✅' : '❌'}
                        </div>
                        <div class="metric-label">JSON格式</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value ${qualityClass}">${(result.result_quality * 100).toFixed(0)}%</div>
                        <div class="metric-label">质量评分</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value">${result.response_time.toFixed(2)}s</div>
                        <div class="metric-label">响应时间</div>
                    </div>
                    <div class="metric">
                        <div class="metric-value ${(result.completeness_score * 100).toFixed(0)}%"}>${(result.completeness_score * 100).toFixed(0)}%</div>
                        <div class="metric-label">完整性</div>
                    </div>
                </div>
                
                ${result.error_message ? `
                    <div style="margin-top: 20px; padding: 15px; background: #f8d7da; border-radius: 8px; color: #721c24;">
                        <strong>错误信息:</strong> ${result.error_message}
                    </div>
                ` : ''}
                
                <div style="margin-top: 20px;">
                    <small>测试时间: ${new Date(result.tested_at).toLocaleString()}</small>
                </div>
            `;
        }

        function refreshPromptList() {
            loadPromptList();
        }

        // Show prompt content
        function showPromptContent(promptNumber, promptId) {
            console.log(`Showing prompt ${promptNumber} content:`, promptId);

            const nameElement = document.getElementById(`prompt${promptNumber}-name`);
            const typeElement = document.getElementById(`prompt${promptNumber}-type`);
            const templateElement = document.getElementById(`prompt${promptNumber}-template`);

            if (!nameElement || !typeElement || !templateElement) {
                console.error(`Cannot find display elements for prompt ${promptNumber}`);
                return;
            }

            if (!promptId) {
                nameElement.textContent = t('prompts.selectPromptFirst') || '请选择提示词';
                typeElement.textContent = '';
                templateElement.textContent = t('prompts.contentWillShow') || '选择提示词后将在此显示完整内容...';
                return;
            }

            const prompt = prompts.find(p => p.id === promptId);
            if (!prompt) {
                console.error(`Prompt not found:`, promptId);
                nameElement.textContent = t('prompts.promptNotFound') || '提示词不存在';
                typeElement.textContent = '';
                templateElement.textContent = t('prompts.promptNotFoundDesc') || '未找到对应的提示词';
                return;
            }

            console.log(`Found prompt:`, prompt.name);

            // Display prompt information
            nameElement.textContent = prompt.name;
            typeElement.textContent = getTaskTypeName(prompt.task_type);
            templateElement.textContent = prompt.template;

            console.log(`Prompt ${promptNumber} content displayed`);
        }

        // 添加辅助函数
        function showPrompt1Content() {
            const promptId = document.getElementById('compare-prompt1').value;
            showPromptContent(1, promptId);
        }

        function showPrompt2Content() {
            const promptId = document.getElementById('compare-prompt2').value;
            showPromptContent(2, promptId);
        }

        // Load document list
        let documents = [];

        async function loadDocumentList() {
            try {
                console.log('Starting to load document list...');
                const response = await fetch('/api/v1/documents');
                console.log('API response status:', response.status);

                if (response.ok) {
                    documents = await response.json();
                    console.log('Got documents:', documents.length, 'items');
                    console.log('Document list:', documents);
                    updateDocumentSelects();
                } else {
                    console.error('Failed to get document list:', response.status, response.statusText);
                    // Try to get error details
                    const errorText = await response.text();
                    console.error('Error details:', errorText);
                }
            } catch (error) {
                console.error('Failed to load document list:', error);
            }
        }

        function updateDocumentSelects() {
            const selects = ['compare-doc-select', 'test-doc-select'];

            selects.forEach(selectId => {
                const select = document.getElementById(selectId);
                if (!select) return;

                const currentValue = select.value;

                select.innerHTML = '<option value="">-- Select existing document --</option>' +
                    documents.map(doc =>
                        `<option value="${doc.doc_id}">${doc.title || doc.doc_id} (${doc.doc_id})</option>`
                    ).join('');

                select.value = currentValue;
            });
        }

        // Load document for comparison
        async function loadDocumentForComparison(docId) {
            if (!docId) return;

            try {
                const response = await fetch(`/api/v1/documents/${docId}`);
                if (response.ok) {
                    const doc = await response.json();
                    document.getElementById('compare-document').value = doc.text || '';
                } else {
                    alert('Failed to load document');
                }
            } catch (error) {
                console.error('Failed to load document:', error);
                alert('Failed to load document');
            }
        }

        // Load document for test
        async function loadDocumentForTest(docId) {
            if (!docId) return;

            try {
                const response = await fetch(`/api/v1/documents/${docId}`);
                if (response.ok) {
                    const doc = await response.json();
                    document.getElementById('test-document').value = doc.text || '';
                } else {
                    alert('Failed to load document');
                }
            } catch (error) {
                console.error('Failed to load document:', error);
                alert('Failed to load document');
            }
        }

        // Trigger file upload
        function triggerFileUpload(type) {
            const fileInput = document.getElementById(`${type}-file-input`);
            fileInput.click();
        }

        // Handle file upload
        async function handleFileUpload(input, type) {
            const file = input.files[0];
            if (!file) return;

            console.log(`Starting file upload: ${file.name}, type: ${type}`);

            // Check file size (limit to 10MB)
            if (file.size > 10 * 1024 * 1024) {
                alert('File size cannot exceed 10MB');
                return;
            }

            // Show upload progress
            const targetTextarea = document.getElementById(`${type}-document`);
            targetTextarea.value = 'Uploading file, please wait...';

            try {
                // Create FormData
                const formData = new FormData();
                formData.append('file', file);
                formData.append('title', file.name.replace(/\.[^/.]+$/, "")); // Remove file extension as title

                // Upload file
                const response = await fetch('/api/v1/documents/upload', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();
                    console.log('File upload successful:', result);

                    // Fill document content into textarea
                    targetTextarea.value = result.text || '';

                    // Reload document list
                    await loadDocumentList();

                    // Show success message
                    alert(`File "${file.name}" uploaded successfully!`);
                } else {
                    const errorData = await response.json();
                    console.error('File upload failed:', errorData);
                    alert(`File upload failed: ${errorData.detail || 'Unknown error'}`);
                    targetTextarea.value = '';
                }
            } catch (error) {
                console.error('File upload error:', error);
                alert(`File upload failed: ${error.message}`);
                targetTextarea.value = '';
            }

            // Clear file input
            input.value = '';
        }

        // 主题切换功能
        function toggleTheme() {
            const html = document.documentElement;
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('theme', newTheme);
            
            // 更新按钮图标
            const themeToggle = document.querySelector('.theme-toggle');
            themeToggle.innerHTML = newTheme === 'dark' ? '☀️' : '🌙';
            themeToggle.title = newTheme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
        }

        // 初始化主题
        function initTheme() {
            const savedTheme = localStorage.getItem('theme') || 'light';
            document.documentElement.setAttribute('data-theme', savedTheme);
            
            const themeToggle = document.querySelector('.theme-toggle');
            themeToggle.innerHTML = savedTheme === 'dark' ? '☀️' : '🌙';
            themeToggle.title = savedTheme === 'dark' ? '切换到亮色主题' : '切换到暗色主题';
        }
    </script>
</body>
</html>